#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级混合图模块
提供多种图融合策略，包括加权融合、注意力融合等
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class HybridGraphFusion(nn.Module):
    """
    高级混合图融合模块
    支持多种融合策略：max、weighted、attention-based
    """
    
    def __init__(self, fusion_type='max', hidden_dim=128, learnable_weights=True):
        """
        Args:
            fusion_type: 融合策略 ('max', 'weighted', 'attention')
            hidden_dim: 隐藏维度（用于attention融合）
            learnable_weights: 是否使用可学习的权重
        """
        super(HybridGraphFusion, self).__init__()
        self.fusion_type = fusion_type
        self.learnable_weights = learnable_weights
        
        if fusion_type == 'weighted' and learnable_weights:
            # 可学习的权重参数
            self.alpha = nn.Parameter(torch.tensor(0.5))  # k-NN图权重
            self.beta = nn.Parameter(torch.tensor(0.5))   # 接触图权重
            
        elif fusion_type == 'attention':
            # 基于注意力的融合
            self.graph_attention = GraphAttentionFusion(hidden_dim)
    
    def e_idx_to_adj(self, E_idx, num_nodes):
        """将k-NN索引转换为邻接矩阵"""
        B, N, K = E_idx.shape
        device = E_idx.device
        
        # 创建邻接矩阵
        adj = torch.zeros(B, N, N, device=device)
        
        # 使用scatter_操作填充
        batch_idx = torch.arange(B, device=device).view(B, 1, 1).expand(B, N, K)
        row_idx = torch.arange(N, device=device).view(1, N, 1).expand(B, N, K)
        
        # 展平索引进行scatter
        batch_flat = batch_idx.reshape(-1)
        row_flat = row_idx.reshape(-1)
        col_flat = E_idx.reshape(-1)
        
        # 创建线性索引
        linear_idx = batch_flat * (N * N) + row_flat * N + col_flat
        adj_flat = adj.view(-1)
        adj_flat.scatter_(0, linear_idx, 1)
        
        adj = adj_flat.view(B, N, N)
        
        # 强制对称
        adj_symmetric = torch.max(adj, adj.transpose(1, 2))
        
        return adj_symmetric
    
    def forward(self, adj_contact, E_idx, node_features=None):
        """
        融合接触图和k-NN图
        
        Args:
            adj_contact: 接触图邻接矩阵 [B, N, N]
            E_idx: k-NN邻居索引 [B, N, K]
            node_features: 节点特征 [B, N, D] (仅attention融合需要)
            
        Returns:
            adj_hybrid: 混合图邻接矩阵 [B, N, N]
        """
        # 将k-NN索引转换为邻接矩阵
        adj_knn = self.e_idx_to_adj(E_idx, adj_contact.size(1))
        
        if self.fusion_type == 'max':
            # 简单的最大值融合
            adj_hybrid = torch.max(adj_knn, adj_contact)
            
        elif self.fusion_type == 'weighted':
            if self.learnable_weights:
                # 使用可学习权重
                alpha = torch.sigmoid(self.alpha)  # 确保权重在[0,1]
                beta = torch.sigmoid(self.beta)
                # 归一化权重
                total_weight = alpha + beta
                alpha = alpha / total_weight
                beta = beta / total_weight
            else:
                # 固定权重
                alpha, beta = 0.6, 0.4
            
            adj_hybrid = alpha * adj_knn + beta * adj_contact
            # 二值化（可选）
            adj_hybrid = (adj_hybrid > 0.5).float()
            
        elif self.fusion_type == 'attention':
            if node_features is None:
                raise ValueError("Attention fusion requires node_features")
            adj_hybrid = self.graph_attention(adj_knn, adj_contact, node_features)
            
        else:
            raise ValueError(f"Unknown fusion type: {self.fusion_type}")
        
        return adj_hybrid

class GraphAttentionFusion(nn.Module):
    """基于注意力机制的图融合"""
    
    def __init__(self, hidden_dim):
        super(GraphAttentionFusion, self).__init__()
        self.hidden_dim = hidden_dim
        
        # 注意力网络
        self.attention_net = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 2),  # 输出两个图的权重
            nn.Softmax(dim=-1)
        )
    
    def forward(self, adj_knn, adj_contact, node_features):
        """
        基于节点特征计算图融合权重
        
        Args:
            adj_knn: k-NN图 [B, N, N]
            adj_contact: 接触图 [B, N, N]
            node_features: 节点特征 [B, N, D]
        """
        B, N, D = node_features.shape
        
        # 计算全局图表示
        # 简化版本：使用平均池化
        global_knn = torch.mean(node_features, dim=1)  # [B, D]
        global_contact = torch.mean(node_features, dim=1)  # [B, D]
        
        # 拼接全局表示
        global_repr = torch.cat([global_knn, global_contact], dim=-1)  # [B, 2*D]
        
        # 计算注意力权重
        attention_weights = self.attention_net(global_repr)  # [B, 2]
        
        # 应用权重
        w_knn = attention_weights[:, 0].view(B, 1, 1)
        w_contact = attention_weights[:, 1].view(B, 1, 1)
        
        adj_hybrid = w_knn * adj_knn + w_contact * adj_contact
        
        # 二值化
        adj_hybrid = (adj_hybrid > 0.5).float()
        
        return adj_hybrid

class AdaptiveHybridGraph(nn.Module):
    """
    自适应混合图模块
    根据层数和训练阶段动态调整融合策略
    """
    
    def __init__(self, num_layers, hidden_dim=128):
        super(AdaptiveHybridGraph, self).__init__()
        self.num_layers = num_layers
        
        # 为每一层创建不同的融合策略
        self.layer_fusers = nn.ModuleList([
            HybridGraphFusion(
                fusion_type='weighted' if i < num_layers // 2 else 'attention',
                hidden_dim=hidden_dim,
                learnable_weights=True
            ) for i in range(num_layers)
        ])
    
    def forward(self, adj_contact, E_idx, layer_idx, node_features=None):
        """
        根据层索引选择合适的融合策略
        
        Args:
            adj_contact: 接触图
            E_idx: k-NN索引
            layer_idx: 当前层索引
            node_features: 节点特征
        """
        fuser = self.layer_fusers[layer_idx]
        return fuser(adj_contact, E_idx, node_features)

# 简化版本的融合函数（向后兼容）
def e_idx_to_adj(E_idx, num_nodes):
    """
    简化版本的E_idx到邻接矩阵转换
    保持与原始实现的兼容性
    """
    B, N, K = E_idx.shape
    device = E_idx.device

    # 创建一个空的邻接矩阵
    adj = torch.zeros(B, N, N, device=device)

    # 创建行索引，用于scatter_操作
    row_indices = torch.arange(N, device=device).view(1, N, 1).expand(B, N, K)

    # 在 E_idx 指定的列位置填充1
    adj.scatter_(2, E_idx, 1)

    # k-NN图不保证对称性，通过与转置取并集来强制对称
    adj_transposed = adj.transpose(1, 2)
    adj_symmetric = torch.max(adj, adj_transposed)

    return adj_symmetric

def create_hybrid_graph(adj_contact, E_idx, fusion_type='max', alpha=0.6):
    """
    创建混合图的便捷函数
    
    Args:
        adj_contact: 接触图邻接矩阵
        E_idx: k-NN邻居索引
        fusion_type: 融合类型 ('max', 'weighted', 'sum')
        alpha: k-NN图的权重（仅weighted时使用）
    
    Returns:
        混合图邻接矩阵
    """
    adj_knn = e_idx_to_adj(E_idx, adj_contact.size(1))
    
    if fusion_type == 'max':
        return torch.max(adj_knn, adj_contact)
    elif fusion_type == 'weighted':
        beta = 1.0 - alpha
        hybrid = alpha * adj_knn + beta * adj_contact
        return (hybrid > 0.5).float()
    elif fusion_type == 'sum':
        return torch.clamp(adj_knn + adj_contact, 0, 1)
    else:
        raise ValueError(f"Unknown fusion type: {fusion_type}")
