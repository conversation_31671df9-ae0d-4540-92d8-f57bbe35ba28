#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证优化效果的快速测试脚本
比较原始配置 vs 优化配置的性能
"""
import os
import warnings
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from tqdm import tqdm
import random
import argparse
from model import MVGNN
from utils import Seed_everything, Metric, TaskDataset
import glob

warnings.simplefilter('ignore')

def load_protein_data_from_tensors(feature_dir='./feature/', max_proteins=10):
    """从tensor文件加载蛋白质数据"""
    x_files = glob.glob(os.path.join(feature_dir, '*_X.tensor'))
    protein_ids = [os.path.basename(f).replace('_X.tensor', '') for f in x_files]
    protein_ids = sorted(protein_ids)[:max_proteins]
    
    protein_data = {}
    
    for pdb_id in protein_ids:
        try:
            X = torch.load(os.path.join(feature_dir, f'{pdb_id}_X.tensor'))
            node_features = torch.load(os.path.join(feature_dir, f'{pdb_id}_node_feature.tensor'))
            mask = torch.load(os.path.join(feature_dir, f'{pdb_id}_mask.tensor'))
            labels = torch.load(os.path.join(feature_dir, f'{pdb_id}_label.tensor'))
            adj = torch.load(os.path.join(feature_dir, f'{pdb_id}_adj.tensor'))
            
            try:
                pe_lap = torch.load(os.path.join(feature_dir, f'{pdb_id}_pe_lap.tensor'))
                pe = pe_lap
            except:
                pe = torch.zeros(X.shape[0], 32)
            
            protein_data[pdb_id] = (X, node_features, mask, labels, adj, pe)
            
        except Exception as e:
            print(f"✗ 加载 {pdb_id} 失败: {e}")
            continue
    
    return protein_data

def test_configuration(protein_data, config, test_name):
    """测试指定配置的模型"""
    print(f"\n🧪 测试配置: {test_name}")
    print("="*60)
    
    available_ids = list(protein_data.keys())
    small_df = pd.DataFrame({'ID': available_ids})
    small_dataset = TaskDataset(small_df, protein_data, 'label')
    
    dataloader = DataLoader(
        small_dataset,
        batch_size=config['batch_size'],
        collate_fn=small_dataset.collate_fn,
        shuffle=False,
        drop_last=False
    )
    
    # 初始化模型
    model = MVGNN(
        config['node_features'], 
        config['edge_features'], 
        config['hidden_dim'], 
        config['num_encoder_layers'], 
        config['k_neighbors'], 
        config['augment_eps'], 
        config['dropout']
    )
    model.cuda()
    
    # 损失函数
    if config['loss_type'] == 'bce':
        criterion = nn.BCEWithLogitsLoss()
    else:
        from loss import FocalLoss
        criterion = FocalLoss()
    
    # 优化器
    if config['optimizer'] == 'adam':
        optimizer = torch.optim.Adam(
            model.parameters(), 
            lr=config['learning_rate'],
            weight_decay=config['weight_decay']
        )
    else:
        from noam_opt import get_std_opt
        optimizer = get_std_opt('PRO', model.parameters(), config['hidden_dim'])
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"图策略: {config['graph_strategy']}")
    print(f"损失函数: {config['loss_type']}")
    print(f"优化器: {config['optimizer']}")
    
    # 训练
    model.train()
    epochs = config['epochs']
    
    best_auc = 0
    convergence_epoch = -1
    
    for epoch in range(epochs):
        total_loss = 0
        all_preds = []
        all_labels = []
        
        for batch_idx, data in enumerate(dataloader):
            _, protein_X, protein_node_features, protein_masks, y, adj, pe = data
            
            protein_X = protein_X.cuda()
            protein_node_features = protein_node_features.cuda()
            protein_masks = protein_masks.cuda()
            y = y.cuda()
            adj = adj.cuda()
            pe = pe.cuda()
            
            optimizer.zero_grad()
            outputs = model(protein_X, protein_node_features, protein_masks, adj, pe, 
                          graph_strategy=config['graph_strategy'])
            
            loss = 0
            batch_preds = []
            batch_labels = []
            
            for i in range(len(protein_masks)):
                mask = protein_masks[i].bool()
                if mask.sum() > 0:
                    masked_outputs = outputs[i][mask]
                    masked_labels = y[i][mask]
                    loss += criterion(masked_outputs, masked_labels)
                    
                    probs = torch.sigmoid(masked_outputs)
                    batch_preds.append(probs.detach().cpu().numpy())
                    batch_labels.append(masked_labels.detach().cpu().numpy())
            
            loss = loss / len(protein_masks)
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            total_loss += loss.item()
            all_preds.extend(batch_preds)
            all_labels.extend(batch_labels)
        
        # 计算指标
        if all_preds and all_labels:
            preds_flat = np.concatenate([p.flatten() for p in all_preds])
            labels_flat = np.concatenate([l.flatten() for l in all_labels])
            
            if len(np.unique(labels_flat)) > 1:
                auc, auprc, mcc, acc, precision, recall, f1 = Metric(preds_flat, labels_flat)
                
                if auc > best_auc:
                    best_auc = auc
                
                if epoch % 5 == 0 or epoch < 5:
                    print(f"Epoch {epoch+1:3d}: Loss={total_loss:.6f}, AUC={auc:.4f}, AUPRC={auprc:.4f}, F1={f1:.4f}")
                
                if auc > 0.95 and convergence_epoch == -1:
                    convergence_epoch = epoch + 1
                    print(f"🎯 收敛到AUC>0.95: Epoch {convergence_epoch}")
    
    print(f"📊 最终结果: 最佳AUC={best_auc:.4f}, 收敛轮次={convergence_epoch if convergence_epoch > 0 else '未收敛'}")
    return best_auc, convergence_epoch

def main():
    parser = argparse.ArgumentParser(description='Validate MVGNN Optimizations')
    parser.add_argument('--seed', type=int, default=2024, help='Random seed')
    parser.add_argument('--max_proteins', type=int, default=10, help='Maximum number of proteins to test')
    parser.add_argument('--epochs', type=int, default=30, help='Number of epochs for quick test')
    args = parser.parse_args()
    
    Seed_everything(args.seed)
    
    print("加载蛋白质数据...")
    protein_data = load_protein_data_from_tensors(max_proteins=args.max_proteins)
    print(f"加载了 {len(protein_data)} 个蛋白质的特征数据")
    
    # 基础配置
    base_config = {
        'node_features': 1024 + 14 + 20,
        'edge_features': 16,
        'hidden_dim': 128,
        'num_encoder_layers': 2,  # 减少层数以加快测试
        'k_neighbors': 30,
        'augment_eps': 0.0,
        'dropout': 0.2,
        'batch_size': 4,
        'epochs': args.epochs,
        'learning_rate': 1e-3,
        'weight_decay': 5e-5,
    }
    
    # 测试配置
    configs = [
        {
            **base_config,
            'graph_strategy': 'hybrid',
            'loss_type': 'focal',
            'optimizer': 'noam',
            'name': '原始配置 (混合图 + Focal Loss + NoamOpt)'
        },
        {
            **base_config,
            'graph_strategy': 'knn_only',
            'loss_type': 'bce',
            'optimizer': 'adam',
            'name': '🚀 优化配置 (k-NN图 + BCE Loss + Adam)'
        },
        {
            **base_config,
            'graph_strategy': 'contact_only',
            'loss_type': 'bce',
            'optimizer': 'adam',
            'name': '对比配置 (接触图 + BCE Loss + Adam)'
        }
    ]
    
    results = []
    
    for config in configs:
        test_name = config.pop('name')
        best_auc, convergence_epoch = test_configuration(protein_data, config, test_name)
        results.append({
            'config': test_name,
            'best_auc': best_auc,
            'convergence_epoch': convergence_epoch
        })
    
    # 总结结果
    print(f"\n{'='*70}")
    print("🏆 优化验证结果总结")
    print(f"{'='*70}")
    
    for result in results:
        convergence_str = f"{result['convergence_epoch']}" if result['convergence_epoch'] > 0 else "未收敛"
        print(f"{result['config']:40s}: AUC={result['best_auc']:.4f}, 收敛轮次={convergence_str}")
    
    # 分析优化效果
    if len(results) >= 2:
        original_auc = results[0]['best_auc']
        optimized_auc = results[1]['best_auc']
        improvement = optimized_auc - original_auc
        
        print(f"\n📈 优化效果分析:")
        print(f"   原始配置 AUC: {original_auc:.4f}")
        print(f"   优化配置 AUC: {optimized_auc:.4f}")
        print(f"   性能提升: {improvement:+.4f}")
        
        if improvement > 0.01:
            print("✅ 优化配置显著提升了模型性能！")
        elif improvement > 0:
            print("🔄 优化配置略有提升")
        else:
            print("❌ 优化配置未能提升性能，可能需要进一步调整")
        
        # 收敛速度比较
        original_conv = results[0]['convergence_epoch']
        optimized_conv = results[1]['convergence_epoch']
        
        if original_conv > 0 and optimized_conv > 0:
            if optimized_conv < original_conv:
                print(f"🚀 优化配置收敛更快: {optimized_conv} vs {original_conv} 轮")
            elif optimized_conv == original_conv:
                print(f"⚖️ 收敛速度相同: {optimized_conv} 轮")
            else:
                print(f"🐌 优化配置收敛较慢: {optimized_conv} vs {original_conv} 轮")
    
    print(f"\n💡 建议:")
    print("1. 如果优化配置表现更好，建议在完整数据集上使用优化配置")
    print("2. 可以使用 train_optimized.py 进行完整训练")
    print("3. 或者使用 train.py --optimized_mode --graph_strategy knn_only")

if __name__ == '__main__':
    main()
