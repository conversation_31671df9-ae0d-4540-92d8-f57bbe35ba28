from self_attention import *
from edge_features import EdgeFeatures

# --- 定义超参数 ---
# 将PE维度和其嵌入维度定义为可配置的变量
PE_DIM = 32  # 必须与您在第一步中生成PE时使用的维度一致
PE_HIDDEN_DIM = 32 # PE特征经过线性层后映射到的维度，可以自己调整
# --- 定义结束 ---

class MVGNN(nn.Module):
    def __init__(self, node_features, edge_features, hidden_dim, num_encoder_layers=4, k_neighbors=30, augment_eps=0., dropout=0.2):
        super(MVGNN, self).__init__()

        # Hyperparameters
        self.augment_eps = augment_eps

        # Edge featurization layers augment_eps
        self.EdgeFeatures = EdgeFeatures(edge_features, top_k=k_neighbors, augment_eps=augment_eps)
        self.dropout = nn.Dropout(dropout)

        # Embedding layers
        self.act_fn = nn.ReLU()
        self.lin1 = nn.Linear(node_features, 512, bias=True)
        self.lin2 = nn.Linear(512, 256, bias=False)
        self.W_v = nn.Linear(256, hidden_dim, bias=True)
        self.W_e = nn.Linear(edge_features, hidden_dim, bias=True)

        # --- 新增的代码：为图拓扑PE创建专属处理层 ---
        self.pe_norm = Normalize(PE_DIM)
        self.pe_lin = nn.Linear(PE_DIM, PE_HIDDEN_DIM)

        # 创建一个融合层，将主节点嵌入和PE嵌入融合回模型的标准hidden_dim
        # 输入维度 = W_v的输出维度(hidden_dim) + pe_lin的输出维度(PE_HIDDEN_DIM)
        self.fusion_layer = nn.Linear(hidden_dim + PE_HIDDEN_DIM, hidden_dim)
        # --- 新增结束 ---
        # self.dropout = dropout
        # Encoder layers
        self.encoder_layers = nn.ModuleList([
            TransformerLayer(hidden_dim, hidden_dim * 2, dropout=dropout)
            for _ in range(num_encoder_layers)
        ])
        self.W_out = nn.Linear(hidden_dim * 2, hidden_dim, bias=True)
        self.W_out1 = nn.Linear(hidden_dim, 64, bias=True)
        self.W_out2 = nn.Linear(64, 1, bias=True)

        # Initialization
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)


    def forward(self, X, V, mask, adj, pe, debug_mode=False):
        # Prepare node and edge embeddings
        # X is the alpha-C coordinate matrix; V is the pre-computed and normalized features ProtTrans+DSSP
        E, E_idx = self.EdgeFeatures(X, mask) # X [B, L, 3] mask [B, L] => E [B, L, K, d_edge]; E_idx [B, L, K]

        # Data augmentation
        if self.training and self.augment_eps > 0:
            V = V + 0.1 * self.augment_eps * torch.randn_like(V)

        if debug_mode:
            print(f"🔧 DEBUG: Input shapes - X: {X.shape}, V: {V.shape}, mask: {mask.shape}, adj: {adj.shape}, pe: {pe.shape}")

        # === 调试模式：简化节点特征处理 ===
        if debug_mode:
            print("🔧 DEBUG MODE: 跳过PE融合，使用简化的节点特征处理")
            # 简化版本：直接处理主要特征，跳过PE
            h_V_main = self.act_fn(self.lin1(V))
            h_V_main = self.act_fn(self.lin2(h_V_main))
            h_V = self.W_v(h_V_main) # -> 形状 [B, L, hidden_dim]
            if debug_mode:
                print(f"🔧 DEBUG: h_V shape after simplified processing: {h_V.shape}")
        else:
            # --- 原始的PE融合逻辑 ---
            # a. 像原来一样处理主要的节点特征 V (ProtT5, DSSP, BLOSUM62等)
            h_V_main = self.act_fn(self.lin1(V))
            h_V_main = self.act_fn(self.lin2(h_V_main))
            h_V_main = self.W_v(h_V_main) # -> 形状 [B, L, hidden_dim]

            # b. 独立处理图拓扑PE特征 pe
            pe_processed = self.pe_norm(pe)
            h_pe = self.pe_lin(pe_processed) # -> 形状 [B, L, PE_HIDDEN_DIM]

            # c. 拼接融合
            h_V_fused = torch.cat((h_V_main, h_pe), dim=-1) # -> 形状 [B, L, hidden_dim + PE_HIDDEN_DIM]

            # d. 通过融合层，得到最终的、信息更丰富的初始节点表示
            h_V = self.act_fn(self.fusion_layer(h_V_fused)) # -> 形状 [B, L, hidden_dim]
            # --- 修改结束 ---

        ho = h_V
        h_E = self.W_e(E)

        mask_attend = gather_nodes(mask.unsqueeze(-1),  E_idx).squeeze(-1)
        mask_attend = mask.unsqueeze(-1) * mask_attend # mask_attend [B, L, K]
        for i,layer in enumerate(self.encoder_layers):
            h_EV = cat_neighbors_nodes(h_V, h_E, E_idx)
            # --- 核心修改：将 adj 和 E_idx 都传入 TransformerLayer ---
            h_V = layer(h_V, h_EV, adj, E_idx, ho, i+1, mask_V=mask, mask_attend=mask_attend)
        logits = self.act_fn(self.W_out1(h_V))
        logits = self.W_out2(logits).squeeze(-1)
        # [B, L]
        return logits
