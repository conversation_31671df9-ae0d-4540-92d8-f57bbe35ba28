#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
图策略消融实验
比较k-NN图、接触图和混合图的性能
"""
import os
import warnings
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from tqdm import tqdm
import random
import argparse
from model import MVGNN
from utils import Seed_everything, Metric, TaskDataset
from loss import FocalLoss
import glob

warnings.simplefilter('ignore')

def load_protein_data_from_tensors(feature_dir='./feature/', max_proteins=4):
    """从tensor文件加载蛋白质数据"""
    x_files = glob.glob(os.path.join(feature_dir, '*_X.tensor'))
    protein_ids = [os.path.basename(f).replace('_X.tensor', '') for f in x_files]
    protein_ids = sorted(protein_ids)[:max_proteins]
    
    protein_data = {}
    
    for pdb_id in protein_ids:
        try:
            X = torch.load(os.path.join(feature_dir, f'{pdb_id}_X.tensor'))
            node_features = torch.load(os.path.join(feature_dir, f'{pdb_id}_node_feature.tensor'))
            mask = torch.load(os.path.join(feature_dir, f'{pdb_id}_mask.tensor'))
            labels = torch.load(os.path.join(feature_dir, f'{pdb_id}_label.tensor'))
            adj = torch.load(os.path.join(feature_dir, f'{pdb_id}_adj.tensor'))
            
            try:
                pe_lap = torch.load(os.path.join(feature_dir, f'{pdb_id}_pe_lap.tensor'))
                pe = pe_lap
            except:
                pe = torch.zeros(X.shape[0], 32)
            
            protein_data[pdb_id] = (X, node_features, mask, labels, adj, pe)
            
        except Exception as e:
            print(f"✗ 加载 {pdb_id} 失败: {e}")
            continue
    
    return protein_data

def test_graph_strategy(protein_data, config, test_name):
    """测试指定图策略的模型"""
    print(f"\n🧪 测试图策略: {test_name}")
    print("="*60)
    
    available_ids = list(protein_data.keys())[:4]
    small_df = pd.DataFrame({'ID': available_ids})
    small_dataset = TaskDataset(small_df, protein_data, 'label')
    
    dataloader = DataLoader(
        small_dataset,
        batch_size=2,
        collate_fn=small_dataset.collate_fn,
        shuffle=False,
        drop_last=False
    )
    
    # 初始化模型
    model = MVGNN(
        config['node_features'], 
        config['edge_features'], 
        config['hidden_dim'], 
        config['num_encoder_layers'], 
        config['k_neighbors'], 
        config['augment_eps'], 
        config['dropout']
    )
    model.cuda()
    
    # 修改模型的图策略
    graph_strategy = config.get('graph_strategy', 'hybrid')
    if hasattr(model, 'encoder_layers'):
        for layer in model.encoder_layers:
            if hasattr(layer, 'graph_strategy'):
                layer.graph_strategy = graph_strategy
    
    criterion = FocalLoss(alpha=0.25, gamma=2.0)
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-3, weight_decay=1e-5)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"图策略: {graph_strategy}")
    
    # 训练
    model.train()
    epochs = 50
    
    best_auc = 0
    convergence_epoch = -1
    
    for epoch in range(epochs):
        total_loss = 0
        all_preds = []
        all_labels = []
        
        for batch_idx, data in enumerate(dataloader):
            _, protein_X, protein_node_features, protein_masks, y, adj, pe = data
            
            protein_X = protein_X.cuda()
            protein_node_features = protein_node_features.cuda()
            protein_masks = protein_masks.cuda()
            y = y.cuda()
            adj = adj.cuda()
            pe = pe.cuda()
            
            # 根据图策略修改邻接矩阵
            if graph_strategy == 'contact_only':
                # 只使用接触图，将k-NN图部分置零
                # 这需要在模型内部实现，这里我们通过传递特殊标记
                pass
            elif graph_strategy == 'knn_only':
                # 只使用k-NN图，将接触图置零
                adj = torch.zeros_like(adj)
            
            optimizer.zero_grad()
            outputs = model(protein_X, protein_node_features, protein_masks, adj, pe, debug_mode=False)
            
            loss = 0
            batch_preds = []
            batch_labels = []
            
            for i in range(len(protein_masks)):
                mask = protein_masks[i].bool()
                if mask.sum() > 0:
                    masked_outputs = outputs[i][mask]
                    masked_labels = y[i][mask]
                    loss += criterion(masked_outputs, masked_labels)
                    
                    probs = torch.sigmoid(masked_outputs)
                    batch_preds.append(probs.detach().cpu().numpy())
                    batch_labels.append(masked_labels.detach().cpu().numpy())
            
            loss = loss / len(protein_masks)
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            total_loss += loss.item()
            all_preds.extend(batch_preds)
            all_labels.extend(batch_labels)
        
        # 计算指标
        if all_preds and all_labels:
            preds_flat = np.concatenate([p.flatten() for p in all_preds])
            labels_flat = np.concatenate([l.flatten() for l in all_labels])
            
            if len(np.unique(labels_flat)) > 1:
                auc, auprc, mcc, acc, precision, recall, f1 = Metric(preds_flat, labels_flat)
                
                if auc > best_auc:
                    best_auc = auc
                
                if epoch % 10 == 0 or epoch < 10:
                    print(f"Epoch {epoch+1:3d}: Loss={total_loss:.6f}, AUC={auc:.4f}, AUPRC={auprc:.4f}, F1={f1:.4f}")
                
                if auc > 0.95 and convergence_epoch == -1:
                    convergence_epoch = epoch + 1
                    print(f"🎯 收敛到AUC>0.95: Epoch {convergence_epoch}")
    
    print(f"📊 最终结果: 最佳AUC={best_auc:.4f}, 收敛轮次={convergence_epoch if convergence_epoch > 0 else '未收敛'}")
    return best_auc, convergence_epoch

def main():
    parser = argparse.ArgumentParser(description='Graph Strategy Ablation Study')
    parser.add_argument('--seed', type=int, default=2024, help='Random seed')
    args = parser.parse_args()
    
    Seed_everything(args.seed)
    
    print("加载蛋白质数据...")
    protein_data = load_protein_data_from_tensors(max_proteins=4)
    print(f"加载了 {len(protein_data)} 个蛋白质的特征数据")
    
    # 基础配置
    base_config = {
        'node_features': 1024 + 14 + 20,
        'edge_features': 16,
        'hidden_dim': 128,
        'num_encoder_layers': 2,
        'k_neighbors': 30,
        'augment_eps': 0.0,
        'dropout': 0.1,
        'debug_mode': False,  # 启用PE融合
    }
    
    # 测试不同的图策略
    strategies = [
        {'strategy': 'knn_only', 'name': '仅k-NN图'},
        {'strategy': 'contact_only', 'name': '仅接触图'},
        {'strategy': 'hybrid', 'name': '混合图（k-NN + 接触图）'},
    ]
    
    results = []
    
    for strategy_config in strategies:
        config = {**base_config, 'graph_strategy': strategy_config['strategy']}
        test_name = strategy_config['name']
        
        best_auc, convergence_epoch = test_graph_strategy(protein_data, config, test_name)
        results.append({
            'strategy': test_name,
            'best_auc': best_auc,
            'convergence_epoch': convergence_epoch
        })
    
    # 总结结果
    print(f"\n{'='*60}")
    print("🏆 图策略消融实验结果总结")
    print(f"{'='*60}")
    
    for result in results:
        print(f"{result['strategy']:25s}: AUC={result['best_auc']:.4f}, 收敛轮次={result['convergence_epoch'] if result['convergence_epoch'] > 0 else '未收敛'}")
    
    # 分析最佳策略
    best_result = max(results, key=lambda x: x['best_auc'])
    print(f"\n🥇 最佳图策略: {best_result['strategy']} (AUC={best_result['best_auc']:.4f})")
    
    # 比较分析
    if len(results) >= 3:
        knn_auc = results[0]['best_auc']
        contact_auc = results[1]['best_auc'] 
        hybrid_auc = results[2]['best_auc']
        
        print(f"\n📊 性能比较:")
        print(f"   混合图 vs k-NN图: {hybrid_auc - knn_auc:+.4f}")
        print(f"   混合图 vs 接触图: {hybrid_auc - contact_auc:+.4f}")
        print(f"   接触图 vs k-NN图: {contact_auc - knn_auc:+.4f}")
        
        if hybrid_auc > max(knn_auc, contact_auc):
            print("✅ 混合图策略表现最佳，证明两种图信息的互补性")
        elif contact_auc > knn_auc:
            print("🔍 接触图比k-NN图更重要，可能需要调整k-NN参数")
        else:
            print("🔍 k-NN图比接触图更重要，可能接触图质量有问题")

if __name__ == '__main__':
    main()
