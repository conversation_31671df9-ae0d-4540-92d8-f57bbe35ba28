#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
稀疏GAT验证脚本
测试PyG稀疏GAT的内存效率和性能
"""

import os
import warnings
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from sklearn.model_selection import train_test_split
from tqdm import tqdm
import time
import argparse
import psutil
import GPUtil
from model import MVGNN
from utils import Seed_everything, Metric, TaskDataset
from loss import FocalLoss

warnings.simplefilter('ignore')

def get_memory_usage():
    """获取当前内存使用情况"""
    # CPU内存
    cpu_memory = psutil.virtual_memory()
    cpu_used_gb = cpu_memory.used / (1024**3)
    cpu_total_gb = cpu_memory.total / (1024**3)
    
    # GPU内存
    gpu_memory = {}
    try:
        gpus = GPUtil.getGPUs()
        for gpu in gpus:
            gpu_memory[f'GPU_{gpu.id}'] = {
                'used_gb': gpu.memoryUsed / 1024,
                'total_gb': gpu.memoryTotal / 1024,
                'utilization': gpu.memoryUtil * 100
            }
    except:
        gpu_memory = {'GPU_0': {'used_gb': 0, 'total_gb': 0, 'utilization': 0}}
    
    return {
        'cpu': {'used_gb': cpu_used_gb, 'total_gb': cpu_total_gb},
        'gpu': gpu_memory
    }

def memory_efficient_test(model, train_loader, val_loader, criterion, optimizer, device, max_epochs=2):
    """
    内存高效的测试，重点监控内存使用
    """
    model.train()
    
    results = {
        'epochs': [],
        'train_loss': [],
        'train_auc': [],
        'val_loss': [],
        'val_auc': [],
        'memory_usage': [],
        'time_per_epoch': [],
        'batch_times': []
    }
    
    print(f"🚀 开始稀疏GAT验证训练（{max_epochs} epochs）...")
    
    # 记录初始内存
    initial_memory = get_memory_usage()
    print(f"📊 初始内存使用:")
    print(f"  CPU: {initial_memory['cpu']['used_gb']:.2f}/{initial_memory['cpu']['total_gb']:.2f} GB")
    for gpu_id, gpu_info in initial_memory['gpu'].items():
        print(f"  {gpu_id}: {gpu_info['used_gb']:.2f}/{gpu_info['total_gb']:.2f} GB ({gpu_info['utilization']:.1f}%)")
    
    for epoch in range(max_epochs):
        start_time = time.time()
        
        # Training phase
        model.train()
        train_loss = 0
        train_preds = []
        train_labels = []
        batch_times = []
        
        for batch_idx, data in enumerate(tqdm(train_loader, desc=f"Epoch {epoch+1}/{max_epochs} - Training")):
            batch_start = time.time()
            
            _, protein_X, protein_node_features, protein_masks, y, adj, pe = data

            # Move data to device
            protein_X = protein_X.to(device)
            protein_node_features = protein_node_features.to(device)
            protein_masks = protein_masks.to(device)
            y = y.to(device)
            adj = adj.to(device)
            pe = pe.to(device)

            # Forward pass
            optimizer.zero_grad()
            
            try:
                outputs = model(protein_X, protein_node_features, protein_masks, adj, pe)
                
                # Calculate loss
                loss = 0
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:
                        loss += criterion(outputs[i][mask], y[i][mask])

                loss = loss / len(protein_masks)
                
                # Backward pass
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                optimizer.step()
                train_loss += loss.item()

                # Collect predictions
                with torch.no_grad():
                    probs = torch.sigmoid(outputs)
                    for i in range(len(protein_masks)):
                        mask = protein_masks[i].bool()
                        if mask.sum() > 0:
                            train_preds.append(probs[i][mask].cpu().numpy())
                            train_labels.append(y[i][mask].cpu().numpy())
                
                batch_time = time.time() - batch_start
                batch_times.append(batch_time)
                
                # 每5个batch监控一次内存
                if batch_idx % 5 == 0:
                    current_memory = get_memory_usage()
                    gpu_used = list(current_memory['gpu'].values())[0]['used_gb']
                    print(f"    Batch {batch_idx}: GPU Memory {gpu_used:.2f}GB, Time {batch_time:.2f}s")
                
            except RuntimeError as e:
                if "out of memory" in str(e):
                    print(f"❌ 内存溢出在batch {batch_idx}: {str(e)}")
                    torch.cuda.empty_cache()
                    return None
                else:
                    raise e

        # Calculate training metrics
        if train_preds:
            train_preds = np.concatenate([p.flatten() for p in train_preds])
            train_labels = np.concatenate([l.flatten() for l in train_labels])
            train_metrics = Metric(train_preds, train_labels)
        else:
            train_metrics = [0.5, 0.0, 0.0, 0.0]  # 默认值

        # Validation phase (简化)
        model.eval()
        val_loss = 0
        val_preds = []
        val_labels = []
        
        with torch.no_grad():
            for data in tqdm(val_loader, desc=f"Epoch {epoch+1}/{max_epochs} - Validation"):
                _, protein_X, protein_node_features, protein_masks, y, adj, pe = data

                protein_X = protein_X.to(device)
                protein_node_features = protein_node_features.to(device)
                protein_masks = protein_masks.to(device)
                y = y.to(device)
                adj = adj.to(device)
                pe = pe.to(device)

                outputs = model(protein_X, protein_node_features, protein_masks, adj, pe)
                
                loss = 0
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:
                        loss += criterion(outputs[i][mask], y[i][mask])
                
                loss = loss / len(protein_masks)
                val_loss += loss.item()
                
                probs = torch.sigmoid(outputs)
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:
                        val_preds.append(probs[i][mask].cpu().numpy())
                        val_labels.append(y[i][mask].cpu().numpy())

        # Calculate validation metrics
        if val_preds:
            val_preds = np.concatenate([p.flatten() for p in val_preds])
            val_labels = np.concatenate([l.flatten() for l in val_labels])
            val_metrics = Metric(val_preds, val_labels)
        else:
            val_metrics = [0.5, 0.0, 0.0, 0.0]
        
        epoch_time = time.time() - start_time
        current_memory = get_memory_usage()
        
        # Store results
        results['epochs'].append(epoch + 1)
        results['train_loss'].append(train_loss / len(train_loader))
        results['train_auc'].append(train_metrics[0])
        results['val_loss'].append(val_loss / len(val_loader))
        results['val_auc'].append(val_metrics[0])
        results['memory_usage'].append(current_memory)
        results['time_per_epoch'].append(epoch_time)
        results['batch_times'].append(np.mean(batch_times))
        
        # Print progress
        print(f"\nEpoch {epoch+1}/{max_epochs} 结果:")
        print(f"  Train Loss: {train_loss/len(train_loader):.6f}, Train AUC: {train_metrics[0]:.6f}")
        print(f"  Val Loss: {val_loss/len(val_loader):.6f}, Val AUC: {val_metrics[0]:.6f}")
        print(f"  时间: {epoch_time:.1f}s, 平均batch时间: {np.mean(batch_times):.2f}s")
        
        # 内存使用报告
        gpu_info = list(current_memory['gpu'].values())[0]
        print(f"  GPU内存: {gpu_info['used_gb']:.2f}/{gpu_info['total_gb']:.2f} GB ({gpu_info['utilization']:.1f}%)")
        print()

    return results

def analyze_sparse_gat_results(results):
    """分析稀疏GAT的性能"""
    if results is None:
        print("❌ 稀疏GAT测试失败 - 内存溢出")
        return "FAILED", 0
    
    print("📊 稀疏GAT性能分析报告")
    print("=" * 50)
    
    # 性能指标
    final_train_auc = results['train_auc'][-1]
    final_val_auc = results['val_auc'][-1]
    avg_batch_time = np.mean(results['batch_times'])
    
    # 内存使用分析
    memory_usages = results['memory_usage']
    max_gpu_memory = max([list(mem['gpu'].values())[0]['used_gb'] for mem in memory_usages])
    
    print(f"🎯 性能指标:")
    print(f"  最终 Train AUC: {final_train_auc:.6f}")
    print(f"  最终 Val AUC: {final_val_auc:.6f}")
    print(f"  平均batch时间: {avg_batch_time:.3f}s")
    print(f"  最大GPU内存使用: {max_gpu_memory:.2f}GB")
    print()
    
    # 成功标准
    is_working = final_train_auc > 0.52
    is_efficient = max_gpu_memory < 8.0  # 8GB以下认为高效
    is_fast = avg_batch_time < 5.0  # 5秒以下认为快速
    
    score = 0
    if is_working:
        print(f"  ✅ 功能正常: PASS (Train AUC {final_train_auc:.3f} > 0.52)")
        score += 40
    else:
        print(f"  ❌ 功能异常: FAIL (Train AUC {final_train_auc:.3f} ≤ 0.52)")
    
    if is_efficient:
        print(f"  ✅ 内存高效: PASS (最大使用 {max_gpu_memory:.2f}GB < 8GB)")
        score += 30
    else:
        print(f"  ❌ 内存低效: FAIL (最大使用 {max_gpu_memory:.2f}GB ≥ 8GB)")
    
    if is_fast:
        print(f"  ✅ 速度良好: PASS (平均 {avg_batch_time:.3f}s < 5s)")
        score += 30
    else:
        print(f"  ❌ 速度较慢: FAIL (平均 {avg_batch_time:.3f}s ≥ 5s)")
    
    print()
    print(f"🏆 稀疏GAT评分: {score}/100")
    
    if score >= 80:
        recommendation = "EXCELLENT"
        print("🎉 结论: 稀疏GAT表现优秀，建议立即使用！")
    elif score >= 60:
        recommendation = "GOOD"
        print("✅ 结论: 稀疏GAT表现良好，可以使用")
    else:
        recommendation = "NEEDS_WORK"
        print("⚠️  结论: 稀疏GAT需要进一步优化")
    
    return recommendation, score

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--dataset_path", type=str, default='./datasets/')
    parser.add_argument("--feature_path", type=str, default='./feature/')
    parser.add_argument("--seed", type=int, default=2024)
    parser.add_argument("--batch_size", type=int, default=8)  # 较小的batch size
    parser.add_argument("--max_epochs", type=int, default=2)
    parser.add_argument("--sample_ratio", type=float, default=0.2)  # 更小的样本
    
    args = parser.parse_args()
    
    # Set random seed
    Seed_everything(seed=args.seed)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    # 检查PyG是否可用
    try:
        import torch_geometric
        print(f"✅ PyTorch Geometric 版本: {torch_geometric.__version__}")
    except ImportError:
        print("❌ PyTorch Geometric 未安装，请运行: pip install torch_geometric")
        return
    
    # Load and prepare data (与之前相同，但样本更小)
    train_df = pd.read_csv(args.dataset_path + 'PRO_Train335.csv')
    
    protein_data = {}
    available_ids = []
    
    for pdb_id in tqdm(train_df['ID'].unique(), desc="Loading protein data"):
        try:
            pe_path = args.feature_path + f"{pdb_id}_pe_rw.tensor"
            if os.path.exists(pe_path):
                pe_tensor = torch.load(pe_path)
            else:
                temp_X = torch.load(args.feature_path + f"{pdb_id}_X.tensor")
                pe_tensor = torch.zeros(temp_X.shape[0], 32)

            protein_data[pdb_id] = (
                torch.load(args.feature_path + f"{pdb_id}_X.tensor"),
                torch.load(args.feature_path + f"{pdb_id}_node_feature.tensor"),
                torch.load(args.feature_path + f"{pdb_id}_mask.tensor"),
                torch.load(args.feature_path + f"{pdb_id}_label.tensor"),
                torch.load(args.feature_path + f"{pdb_id}_adj.tensor"),
                pe_tensor
            )
            available_ids.append(pdb_id)
        except FileNotFoundError:
            continue
    
    # 小样本测试
    sample_size = int(len(available_ids) * args.sample_ratio)
    sampled_ids = np.random.choice(available_ids, sample_size, replace=False)
    train_ids, val_ids = train_test_split(sampled_ids, test_size=0.3, random_state=args.seed)
    
    train_df_sample = train_df[train_df['ID'].isin(train_ids)].reset_index(drop=True)
    val_df_sample = train_df[train_df['ID'].isin(val_ids)].reset_index(drop=True)
    
    print(f"📊 稀疏GAT测试数据: {len(train_ids)} 训练, {len(val_ids)} 验证")
    
    # Create datasets
    train_dataset = TaskDataset(train_df_sample, protein_data, 'label')
    val_dataset = TaskDataset(val_df_sample, protein_data, 'label')
    
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, 
                             collate_fn=train_dataset.collate_fn, num_workers=1)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False,
                           collate_fn=val_dataset.collate_fn, num_workers=1)
    
    # Initialize model with sparse GAT
    model = MVGNN(
        node_features=1024 + 14 + 20,
        edge_features=16,
        hidden_dim=128,
        num_encoder_layers=2,
        k_neighbors=20,
        augment_eps=0.05,
        dropout=0.3
    ).to(device)
    
    print(f"🏗️  模型参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # Simple optimizer and loss
    criterion = FocalLoss(alpha=0.25, gamma=2.0)
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-4, weight_decay=5e-5)
    
    # Test sparse GAT
    results = memory_efficient_test(model, train_loader, val_loader, criterion, optimizer, device, args.max_epochs)
    
    # Analyze results
    recommendation, score = analyze_sparse_gat_results(results)
    
    # Final recommendation
    print("\n" + "="*50)
    if recommendation == "EXCELLENT":
        print("🚀 建议: 稀疏GAT完美工作，立即开始完整训练！")
        print("推荐命令:")
        print("python train.py --output_path ./output_sparse_gat/ --loss_type combined --use_sam --sam_rho 0.015 --weight_decay 1e-4 --n_folds 5")
    elif recommendation == "GOOD":
        print("✅ 建议: 稀疏GAT工作良好，可以开始训练")
        print("推荐命令:")
        print("python train.py --output_path ./output_sparse_gat/ --loss_type focal --use_sam --sam_rho 0.01 --weight_decay 5e-5 --n_folds 3")
    else:
        print("🔧 建议: 稀疏GAT需要进一步调试")

if __name__ == '__main__':
    main()
