#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
步骤2：恢复NoamOpt + SAM的训练脚本
在CombinedLoss稳定的基础上测试高级优化器
"""
import os
import pickle
import warnings
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, RandomSampler
from sklearn.model_selection import KFold
from tqdm import tqdm
import random
import argparse
from model import MVGNN
from utils import Seed_everything, Metric, Write_log, TaskDataset, get_std_opt
from loss import CombinedLoss

warnings.simplefilter('ignore')

def train_model_step2(train_df, val_df, protein_data, model_class, config, fold, output_root='./output_step2/', args=None):
    """
    步骤2：恢复NoamOpt + SAM的训练函数
    """
    # Setup paths and logging
    output_weight = output_root + "weight/"
    if not os.path.exists(output_weight):
        os.makedirs(output_weight)
    
    log = open(output_weight + f'fold{fold}_step2.log', 'w', buffering=1)
    Write_log(log, "=== STEP 2: NOAM + SAM RESTORATION ===")
    Write_log(log, str(config) + '\n')
    
    # Extract configuration parameters
    node_features = config['node_features']
    edge_features = config['edge_features']
    hidden_dim = config['hidden_dim']
    num_encoder_layers = config['num_encoder_layers']
    k_neighbors = config['k_neighbors']
    augment_eps = config['augment_eps']
    dropout = config['dropout']
    batch_size = config['batch_size']
    epochs = config['epochs']
    patience = config['patience']
    use_sam = config.get('use_sam', True)
    sam_rho = config.get('sam_rho', 0.01)  # 降低SAM的rho值
    
    # Reset DataFrame indices
    train_df_reset = train_df.reset_index(drop=True)
    val_df_reset = val_df.reset_index(drop=True)

    # Create datasets and dataloaders
    train_dataset = TaskDataset(train_df_reset, protein_data, 'label')
    val_dataset = TaskDataset(val_df_reset, protein_data, 'label')

    train_dataloader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        collate_fn=train_dataset.collate_fn,
        num_workers=2,
        prefetch_factor=1
    )
    
    val_dataloader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        collate_fn=val_dataset.collate_fn,
        shuffle=False,
        drop_last=False,
        num_workers=2,
        prefetch_factor=1
    )
    
    # Initialize model
    model = model_class(
        node_features, 
        edge_features, 
        hidden_dim, 
        num_encoder_layers, 
        k_neighbors, 
        augment_eps, 
        dropout
    )
    model.cuda()
    
    Write_log(log, "STEP 2: Using single GPU")
    
    # 保持CombinedLoss
    Write_log(log, "STEP 2: Using CombinedLoss")
    criterion = CombinedLoss(
        focal_alpha=0.25,
        focal_gamma=2.0,
        lovasz_weight=0.2,
        tversky_alpha=0.3,
        tversky_beta=0.7,
        tversky_weight=0.1
    )
    
    # 步骤2：恢复NoamOpt + SAM，但使用保守参数
    Write_log(log, f"STEP 2: Using NoamOpt + SAM with conservative parameters (rho={sam_rho})")
    optimizer = get_std_opt(
        task='PRO',
        parameters=model.parameters(),
        d_model=hidden_dim,
        use_sam=use_sam,
        sam_rho=sam_rho  # 降低SAM的扰动强度
    )
    
    # 定义closure函数用于SAM
    def closure():
        optimizer.zero_grad()
        outputs = model(protein_X, protein_node_features, protein_masks, adj, pe)
        
        loss = 0
        for i in range(len(protein_masks)):
            mask = protein_masks[i].bool()
            if mask.sum() > 0:
                loss += criterion(outputs[i][mask], y[i][mask])
        
        loss = loss / len(protein_masks)
        loss.backward()
        return loss
    
    # Training loop
    best_val_auc = 0
    best_epoch = 0
    no_improvement = 0
    
    for epoch in range(epochs):
        # Training phase
        model.train()
        train_loss = 0
        train_preds = []
        train_labels = []
        
        # 监控梯度和损失组件
        total_grad_norm = 0
        focal_losses = []
        lovasz_losses = []
        tversky_losses = []
        num_batches = 0
        
        for data in tqdm(train_dataloader, desc=f"Epoch {epoch+1}/{epochs} - Training"):
            _, protein_X, protein_node_features, protein_masks, y, adj, pe = data

            # Move data to GPU
            protein_X = protein_X.cuda()
            protein_node_features = protein_node_features.cuda()
            protein_masks = protein_masks.cuda()
            y = y.cuda()
            adj = adj.cuda()
            pe = pe.cuda()

            # 步骤2：使用SAM优化器
            if use_sam:
                # SAM的两步更新
                optimizer.zero_grad()

                # 第一步：计算梯度并扰动参数
                loss = closure()
                
                # 关键修复：梯度裁剪
                grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                total_grad_norm += grad_norm.item()
                
                optimizer.first_step(zero_grad=True)

                # 第二步：在扰动位置重新计算梯度并更新
                closure()
                
                # 关键修复：第二步也加入梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                optimizer.second_step(zero_grad=True)
            else:
                # 标准优化器更新
                optimizer.zero_grad()
                outputs = model(protein_X, protein_node_features, protein_masks, adj, pe)

                loss = 0
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:
                        loss += criterion(outputs[i][mask], y[i][mask])

                loss = loss / len(protein_masks)
                loss.backward()
                
                grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                total_grad_norm += grad_norm.item()
                
                optimizer.step()

            num_batches += 1
            train_loss += loss.item()

            # 收集损失组件用于监控
            with torch.no_grad():
                outputs = model(protein_X, protein_node_features, protein_masks, adj, pe)
                batch_focal = 0
                batch_lovasz = 0
                batch_tversky = 0
                
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:
                        loss_components = criterion.get_loss_components(outputs[i][mask], y[i][mask])
                        batch_focal += loss_components['focal'].item()
                        batch_lovasz += loss_components['lovasz'].item()
                        batch_tversky += loss_components['tversky'].item()
                
                focal_losses.append(batch_focal / len(protein_masks))
                lovasz_losses.append(batch_lovasz / len(protein_masks))
                tversky_losses.append(batch_tversky / len(protein_masks))

            # Collect predictions and labels for metrics
            with torch.no_grad():
                probs = torch.sigmoid(outputs)
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:
                        train_preds.append(probs[i][mask].cpu().numpy())
                        train_labels.append(y[i][mask].cpu().numpy())
        
        # Calculate training metrics
        train_preds = np.concatenate([p.flatten() for p in train_preds])
        train_labels = np.concatenate([l.flatten() for l in train_labels])
        train_metrics = Metric(train_preds, train_labels)
        
        avg_grad_norm = total_grad_norm / num_batches if num_batches > 0 else 0
        avg_focal = np.mean(focal_losses)
        avg_lovasz = np.mean(lovasz_losses)
        avg_tversky = np.mean(tversky_losses)
        
        # Validation phase
        model.eval()
        val_loss = 0
        val_preds = []
        val_labels = []
        
        with torch.no_grad():
            for data in tqdm(val_dataloader, desc=f"Epoch {epoch+1}/{epochs} - Validation"):
                _, protein_X, protein_node_features, protein_masks, y, adj, pe = data

                # Move data to GPU
                protein_X = protein_X.cuda()
                protein_node_features = protein_node_features.cuda()
                protein_masks = protein_masks.cuda()
                y = y.cuda()
                adj = adj.cuda()
                pe = pe.cuda()

                # Forward pass
                outputs = model(protein_X, protein_node_features, protein_masks, adj, pe)
                
                # Calculate loss
                loss = 0
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:
                        loss += criterion(outputs[i][mask], y[i][mask])
                
                loss = loss / len(protein_masks)
                val_loss += loss.item()
                
                # Collect predictions and labels
                probs = torch.sigmoid(outputs)
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:
                        val_preds.append(probs[i][mask].cpu().numpy())
                        val_labels.append(y[i][mask].cpu().numpy())
        
        # Calculate validation metrics
        val_preds = np.concatenate([p.flatten() for p in val_preds])
        val_labels = np.concatenate([l.flatten() for l in val_labels])
        val_metrics = Metric(val_preds, val_labels)
        
        # Enhanced logging
        log_message = (f"Epoch {epoch+1}/{epochs} - "
                      f"Train Loss: {train_loss/len(train_dataloader):.6f}, "
                      f"Val Loss: {val_loss/len(val_dataloader):.6f}, "
                      f"Train AUC: {train_metrics[0]:.6f}, "
                      f"Val AUC: {val_metrics[0]:.6f}, "
                      f"Grad Norm: {avg_grad_norm:.6f}, "
                      f"SAM: {'ON' if use_sam else 'OFF'}, "
                      f"Focal: {avg_focal:.6f}, "
                      f"Lovasz: {avg_lovasz:.6f}, "
                      f"Tversky: {avg_tversky:.6f}")
        
        Write_log(log, log_message)
        
        # Check for improvement
        current_val_auc = val_metrics[0]
        if current_val_auc > best_val_auc:
            best_val_auc = current_val_auc
            best_epoch = epoch
            no_improvement = 0

            # Save the best model
            torch.save(model.state_dict(), output_weight + f'fold{fold}_step2.ckpt')
            Write_log(log, f"Model saved at epoch {epoch+1} with validation AUC: {best_val_auc:.6f}")
        else:
            no_improvement += 1
            
        # Early stopping
        if no_improvement >= patience:
            Write_log(log, f"Early stopping at epoch {epoch+1}. Best epoch: {best_epoch+1} with validation AUC: {best_val_auc:.6f}")
            break

    log.close()
    return best_val_auc

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--dataset_path", type=str, default='./datasets/')
    parser.add_argument("--feature_path", type=str, default='./feature/')
    parser.add_argument("--output_path", type=str, default='./output_step2/')
    parser.add_argument("--task", type=str, default='PRO')
    parser.add_argument("--num_workers", type=int, default=2)
    parser.add_argument("--seed", type=int, default=2024)
    parser.add_argument("--n_folds", type=int, default=1)
    parser.add_argument("--use_sam", action='store_true', default=True)
    parser.add_argument("--sam_rho", type=float, default=0.01)  # 降低默认值
    
    args = parser.parse_args()
    
    # Set random seed
    Seed_everything(seed=args.seed)
    
    # Load training data
    train_df = pd.read_csv(args.dataset_path + 'PRO_Train335.csv')

    # Load protein data
    protein_data = {}
    for pdb_id in tqdm(train_df['ID'].unique(), desc="Loading protein data"):
        try:
            pe_path = args.feature_path + f"{pdb_id}_pe_rw.tensor"
            if os.path.exists(pe_path):
                pe_tensor = torch.load(pe_path)
            else:
                temp_X = torch.load(args.feature_path + f"{pdb_id}_X.tensor")
                pe_tensor = torch.zeros(temp_X.shape[0], 32)

            protein_data[pdb_id] = (
                torch.load(args.feature_path + f"{pdb_id}_X.tensor"),
                torch.load(args.feature_path + f"{pdb_id}_node_feature.tensor"),
                torch.load(args.feature_path + f"{pdb_id}_mask.tensor"),
                torch.load(args.feature_path + f"{pdb_id}_label.tensor"),
                torch.load(args.feature_path + f"{pdb_id}_adj.tensor"),
                pe_tensor
            )
        except FileNotFoundError as e:
            print(f"Warning: Could not load data for {pdb_id}: {e}")
            continue
    
    # 步骤2配置：恢复NoamOpt + SAM
    nn_config = {
        'node_features': 1024 + 14 + 20,
        'edge_features': 16,
        'hidden_dim': 128,
        'num_encoder_layers': 2,  # 保持较少层数
        'k_neighbors': 20,
        'augment_eps': 0.05,
        'dropout': 0.2,
        'id_name': 'ID',
        'obj_max': 1,
        'epochs': 10,
        'patience': 5,
        'batch_size': 16,
        'seed': args.seed,
        'use_sam': args.use_sam,
        'sam_rho': args.sam_rho,
        'remark': f'STEP 2: Hybrid graph with NoamOpt + SAM (rho={args.sam_rho})'
    }
    
    # Create output directories
    output_root = args.output_path
    if not os.path.exists(output_root):
        os.makedirs(output_root)
    if not os.path.exists(output_root + "weight/"):
        os.makedirs(output_root + "weight/")
    
    # Filter training data
    available_ids = list(protein_data.keys())
    train_df = train_df[train_df['ID'].isin(available_ids)]
    print(f"Using {len(available_ids)} proteins for Step 2 training")

    # 只训练一个fold进行测试
    kf = KFold(n_splits=5, shuffle=True, random_state=args.seed)
    train_idx, val_idx = next(iter(kf.split(available_ids)))
    
    print(f"\n{'='*50}\nSTEP 2: NoamOpt + SAM Restoration\n{'='*50}")

    # Split data
    train_ids = [available_ids[i] for i in train_idx]
    val_ids = [available_ids[i] for i in val_idx]

    fold_train_df = train_df[train_df['ID'].isin(train_ids)]
    fold_val_df = train_df[train_df['ID'].isin(val_ids)]
    
    # Train the model
    fold_metric = train_model_step2(
        fold_train_df, 
        fold_val_df, 
        protein_data, 
        MVGNN, 
        nn_config, 
        0, 
        output_root, 
        args
    )
    
    print(f"\nSTEP 2 Result: AUC = {fold_metric:.6f}")
    print("✅ NoamOpt + SAM restoration test completed!")

if __name__ == '__main__':
    main()
