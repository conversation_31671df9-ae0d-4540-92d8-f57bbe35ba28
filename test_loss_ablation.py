#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
损失函数消融实验
比较不同损失函数的性能
"""
import os
import warnings
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from tqdm import tqdm
import random
import argparse
from model import MVGNN
from utils import Seed_everything, Metric, TaskDataset
from loss import FocalLoss, TverskyLoss, LovaszHinge, CombinedLoss
import glob

warnings.simplefilter('ignore')

def load_protein_data_from_tensors(feature_dir='./feature/', max_proteins=4):
    """从tensor文件加载蛋白质数据"""
    x_files = glob.glob(os.path.join(feature_dir, '*_X.tensor'))
    protein_ids = [os.path.basename(f).replace('_X.tensor', '') for f in x_files]
    protein_ids = sorted(protein_ids)[:max_proteins]
    
    protein_data = {}
    
    for pdb_id in protein_ids:
        try:
            X = torch.load(os.path.join(feature_dir, f'{pdb_id}_X.tensor'))
            node_features = torch.load(os.path.join(feature_dir, f'{pdb_id}_node_feature.tensor'))
            mask = torch.load(os.path.join(feature_dir, f'{pdb_id}_mask.tensor'))
            labels = torch.load(os.path.join(feature_dir, f'{pdb_id}_label.tensor'))
            adj = torch.load(os.path.join(feature_dir, f'{pdb_id}_adj.tensor'))
            
            try:
                pe_lap = torch.load(os.path.join(feature_dir, f'{pdb_id}_pe_lap.tensor'))
                pe = pe_lap
            except:
                pe = torch.zeros(X.shape[0], 32)
            
            protein_data[pdb_id] = (X, node_features, mask, labels, adj, pe)
            
        except Exception as e:
            print(f"✗ 加载 {pdb_id} 失败: {e}")
            continue
    
    return protein_data

def test_loss_function(protein_data, config, loss_config, test_name):
    """测试指定损失函数的模型"""
    print(f"\n🧪 测试损失函数: {test_name}")
    print("="*60)
    
    available_ids = list(protein_data.keys())[:4]
    small_df = pd.DataFrame({'ID': available_ids})
    small_dataset = TaskDataset(small_df, protein_data, 'label')
    
    dataloader = DataLoader(
        small_dataset,
        batch_size=2,
        collate_fn=small_dataset.collate_fn,
        shuffle=False,
        drop_last=False
    )
    
    # 初始化模型
    model = MVGNN(
        config['node_features'], 
        config['edge_features'], 
        config['hidden_dim'], 
        config['num_encoder_layers'], 
        config['k_neighbors'], 
        config['augment_eps'], 
        config['dropout']
    )
    model.cuda()
    
    # 根据配置创建损失函数
    loss_type = loss_config['type']
    if loss_type == 'focal':
        criterion = FocalLoss(alpha=loss_config.get('alpha', 0.25), gamma=loss_config.get('gamma', 2.0))
    elif loss_type == 'tversky':
        criterion = TverskyLoss(alpha=loss_config.get('alpha', 0.3), beta=loss_config.get('beta', 0.7))
    elif loss_type == 'lovasz':
        criterion = LovaszHinge()
    elif loss_type == 'combined':
        criterion = CombinedLoss(
            focal_weight=loss_config.get('focal_weight', 0.4),
            tversky_weight=loss_config.get('tversky_weight', 0.5),
            lovasz_weight=loss_config.get('lovasz_weight', 0.1),
            focal_alpha=loss_config.get('focal_alpha', 0.25),
            focal_gamma=loss_config.get('focal_gamma', 2.0),
            tversky_alpha=loss_config.get('tversky_alpha', 0.3),
            tversky_beta=loss_config.get('tversky_beta', 0.7)
        )
    else:
        criterion = nn.BCEWithLogitsLoss()
    
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-3, weight_decay=1e-5)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"损失函数: {loss_type}")
    
    # 训练
    model.train()
    epochs = 50
    
    best_auc = 0
    best_auprc = 0
    convergence_epoch = -1
    
    for epoch in range(epochs):
        total_loss = 0
        all_preds = []
        all_labels = []
        
        for batch_idx, data in enumerate(dataloader):
            _, protein_X, protein_node_features, protein_masks, y, adj, pe = data
            
            protein_X = protein_X.cuda()
            protein_node_features = protein_node_features.cuda()
            protein_masks = protein_masks.cuda()
            y = y.cuda()
            adj = adj.cuda()
            pe = pe.cuda()
            
            # 使用最佳图策略（仅k-NN图）
            adj = torch.zeros_like(adj)  # 清空接触图
            
            optimizer.zero_grad()
            outputs = model(protein_X, protein_node_features, protein_masks, adj, pe, debug_mode=False)
            
            loss = 0
            batch_preds = []
            batch_labels = []
            
            for i in range(len(protein_masks)):
                mask = protein_masks[i].bool()
                if mask.sum() > 0:
                    masked_outputs = outputs[i][mask]
                    masked_labels = y[i][mask]
                    loss += criterion(masked_outputs, masked_labels)
                    
                    probs = torch.sigmoid(masked_outputs)
                    batch_preds.append(probs.detach().cpu().numpy())
                    batch_labels.append(masked_labels.detach().cpu().numpy())
            
            loss = loss / len(protein_masks)
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            total_loss += loss.item()
            all_preds.extend(batch_preds)
            all_labels.extend(batch_labels)
        
        # 计算指标
        if all_preds and all_labels:
            preds_flat = np.concatenate([p.flatten() for p in all_preds])
            labels_flat = np.concatenate([l.flatten() for l in all_labels])
            
            if len(np.unique(labels_flat)) > 1:
                auc, auprc, mcc, acc, precision, recall, f1 = Metric(preds_flat, labels_flat)
                
                if auc > best_auc:
                    best_auc = auc
                if auprc > best_auprc:
                    best_auprc = auprc
                
                if epoch % 10 == 0 or epoch < 10:
                    print(f"Epoch {epoch+1:3d}: Loss={total_loss:.6f}, AUC={auc:.4f}, AUPRC={auprc:.4f}, F1={f1:.4f}")
                
                if auc > 0.95 and convergence_epoch == -1:
                    convergence_epoch = epoch + 1
                    print(f"🎯 收敛到AUC>0.95: Epoch {convergence_epoch}")
    
    print(f"📊 最终结果: 最佳AUC={best_auc:.4f}, 最佳AUPRC={best_auprc:.4f}, 收敛轮次={convergence_epoch if convergence_epoch > 0 else '未收敛'}")
    return best_auc, best_auprc, convergence_epoch

def main():
    parser = argparse.ArgumentParser(description='Loss Function Ablation Study')
    parser.add_argument('--seed', type=int, default=2024, help='Random seed')
    args = parser.parse_args()
    
    Seed_everything(args.seed)
    
    print("加载蛋白质数据...")
    protein_data = load_protein_data_from_tensors(max_proteins=4)
    print(f"加载了 {len(protein_data)} 个蛋白质的特征数据")
    
    # 基础配置（使用最佳设置）
    base_config = {
        'node_features': 1024 + 14 + 20,
        'edge_features': 16,
        'hidden_dim': 128,
        'num_encoder_layers': 2,
        'k_neighbors': 30,
        'augment_eps': 0.0,
        'dropout': 0.1,
        'debug_mode': False,  # 启用PE融合
    }
    
    # 测试不同的损失函数
    loss_configs = [
        {'type': 'bce', 'name': 'BCE Loss'},
        {'type': 'focal', 'alpha': 0.25, 'gamma': 2.0, 'name': 'Focal Loss'},
        {'type': 'tversky', 'alpha': 0.3, 'beta': 0.7, 'name': 'Tversky Loss'},
        {'type': 'lovasz', 'name': 'Lovasz Loss'},
        {'type': 'combined', 'focal_weight': 0.4, 'tversky_weight': 0.5, 'lovasz_weight': 0.1, 'name': 'Combined Loss'},
    ]
    
    results = []
    
    for loss_config in loss_configs:
        test_name = loss_config.pop('name')
        
        best_auc, best_auprc, convergence_epoch = test_loss_function(protein_data, base_config, loss_config, test_name)
        results.append({
            'loss': test_name,
            'best_auc': best_auc,
            'best_auprc': best_auprc,
            'convergence_epoch': convergence_epoch
        })
    
    # 总结结果
    print(f"\n{'='*70}")
    print("🏆 损失函数消融实验结果总结")
    print(f"{'='*70}")
    
    for result in results:
        print(f"{result['loss']:15s}: AUC={result['best_auc']:.4f}, AUPRC={result['best_auprc']:.4f}, 收敛轮次={result['convergence_epoch'] if result['convergence_epoch'] > 0 else '未收敛'}")
    
    # 分析最佳损失函数
    best_auc_result = max(results, key=lambda x: x['best_auc'])
    best_auprc_result = max(results, key=lambda x: x['best_auprc'])
    
    print(f"\n🥇 最佳AUC: {best_auc_result['loss']} (AUC={best_auc_result['best_auc']:.4f})")
    print(f"🥇 最佳AUPRC: {best_auprc_result['loss']} (AUPRC={best_auprc_result['best_auprc']:.4f})")
    
    if best_auc_result['loss'] == best_auprc_result['loss']:
        print(f"✅ {best_auc_result['loss']} 在两个指标上都表现最佳")
    else:
        print(f"🔄 AUC和AUPRC的最佳损失函数不同，需要根据任务重点选择")

if __name__ == '__main__':
    main()
