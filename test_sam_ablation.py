#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
SAM优化器消融实验
比较Adam和SAM优化器的性能
"""
import os
import warnings
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from tqdm import tqdm
import random
import argparse
from model import MVGNN
from utils import Seed_everything, Metric, TaskDataset
from loss import FocalLoss, CombinedLoss
from sam import SAM
import glob

warnings.simplefilter('ignore')

def load_protein_data_from_tensors(feature_dir='./feature/', max_proteins=4):
    """从tensor文件加载蛋白质数据"""
    x_files = glob.glob(os.path.join(feature_dir, '*_X.tensor'))
    protein_ids = [os.path.basename(f).replace('_X.tensor', '') for f in x_files]
    protein_ids = sorted(protein_ids)[:max_proteins]
    
    protein_data = {}
    
    for pdb_id in protein_ids:
        try:
            X = torch.load(os.path.join(feature_dir, f'{pdb_id}_X.tensor'))
            node_features = torch.load(os.path.join(feature_dir, f'{pdb_id}_node_feature.tensor'))
            mask = torch.load(os.path.join(feature_dir, f'{pdb_id}_mask.tensor'))
            labels = torch.load(os.path.join(feature_dir, f'{pdb_id}_label.tensor'))
            adj = torch.load(os.path.join(feature_dir, f'{pdb_id}_adj.tensor'))
            
            try:
                pe_lap = torch.load(os.path.join(feature_dir, f'{pdb_id}_pe_lap.tensor'))
                pe = pe_lap
            except:
                pe = torch.zeros(X.shape[0], 32)
            
            protein_data[pdb_id] = (X, node_features, mask, labels, adj, pe)
            
        except Exception as e:
            print(f"✗ 加载 {pdb_id} 失败: {e}")
            continue
    
    return protein_data

def test_optimizer(protein_data, config, optimizer_config, test_name):
    """测试指定优化器的模型"""
    print(f"\n🧪 测试优化器: {test_name}")
    print("="*60)
    
    available_ids = list(protein_data.keys())[:4]
    small_df = pd.DataFrame({'ID': available_ids})
    small_dataset = TaskDataset(small_df, protein_data, 'label')
    
    dataloader = DataLoader(
        small_dataset,
        batch_size=2,
        collate_fn=small_dataset.collate_fn,
        shuffle=False,
        drop_last=False
    )
    
    # 初始化模型
    model = MVGNN(
        config['node_features'], 
        config['edge_features'], 
        config['hidden_dim'], 
        config['num_encoder_layers'], 
        config['k_neighbors'], 
        config['augment_eps'], 
        config['dropout']
    )
    model.cuda()
    
    # 根据配置创建优化器
    optimizer_type = optimizer_config['type']
    if optimizer_type == 'adam':
        optimizer = torch.optim.Adam(
            model.parameters(), 
            lr=optimizer_config.get('lr', 1e-3),
            weight_decay=optimizer_config.get('weight_decay', 1e-5)
        )
    elif optimizer_type == 'sam':
        base_optimizer = torch.optim.Adam
        optimizer = SAM(
            model.parameters(),
            base_optimizer,
            lr=optimizer_config.get('lr', 1e-3),
            weight_decay=optimizer_config.get('weight_decay', 1e-5),
            rho=optimizer_config.get('rho', 0.05)
        )
    else:
        raise ValueError(f"Unknown optimizer type: {optimizer_type}")
    
    # 使用最佳损失函数（BCE或Combined）
    loss_type = config.get('loss_type', 'bce')
    if loss_type == 'bce':
        criterion = nn.BCEWithLogitsLoss()
    elif loss_type == 'combined':
        criterion = CombinedLoss()
    else:
        criterion = FocalLoss()
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"优化器: {optimizer_type}")
    print(f"损失函数: {loss_type}")
    
    # 训练
    model.train()
    epochs = 60  # 增加epoch数量，观察SAM的长期效果
    
    best_auc = 0
    best_auprc = 0
    convergence_epoch = -1
    
    for epoch in range(epochs):
        total_loss = 0
        all_preds = []
        all_labels = []
        
        for batch_idx, data in enumerate(dataloader):
            _, protein_X, protein_node_features, protein_masks, y, adj, pe = data
            
            protein_X = protein_X.cuda()
            protein_node_features = protein_node_features.cuda()
            protein_masks = protein_masks.cuda()
            y = y.cuda()
            adj = adj.cuda()
            pe = pe.cuda()
            
            # 使用最佳图策略（仅k-NN图）
            adj = torch.zeros_like(adj)  # 清空接触图
            
            if optimizer_type == 'sam':
                # SAM优化器需要两次前向传播
                def closure():
                    optimizer.zero_grad()
                    outputs = model(protein_X, protein_node_features, protein_masks, adj, pe, debug_mode=False)

                    loss = 0
                    for i in range(len(protein_masks)):
                        mask = protein_masks[i].bool()
                        if mask.sum() > 0:
                            masked_outputs = outputs[i][mask]
                            masked_labels = y[i][mask]
                            loss += criterion(masked_outputs, masked_labels)

                    loss = loss / len(protein_masks)
                    loss.backward()
                    return loss

                # 手动实现SAM的两步更新，避免空梯度问题
                optimizer.zero_grad()
                outputs = model(protein_X, protein_node_features, protein_masks, adj, pe, debug_mode=False)

                loss = 0
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:
                        masked_outputs = outputs[i][mask]
                        masked_labels = y[i][mask]
                        loss += criterion(masked_outputs, masked_labels)

                loss = loss / len(protein_masks)
                loss.backward()

                # 检查是否有梯度
                has_grad = any(p.grad is not None for p in model.parameters())
                if has_grad:
                    optimizer.first_step(zero_grad=True)

                    # 第二次前向传播
                    outputs = model(protein_X, protein_node_features, protein_masks, adj, pe, debug_mode=False)
                    loss = 0
                    for i in range(len(protein_masks)):
                        mask = protein_masks[i].bool()
                        if mask.sum() > 0:
                            masked_outputs = outputs[i][mask]
                            masked_labels = y[i][mask]
                            loss += criterion(masked_outputs, masked_labels)

                    loss = loss / len(protein_masks)
                    loss.backward()
                    optimizer.second_step(zero_grad=True)
                else:
                    print("Warning: No gradients found, skipping SAM step")
                    optimizer.zero_grad()
            else:
                # 标准优化器
                optimizer.zero_grad()
                outputs = model(protein_X, protein_node_features, protein_masks, adj, pe, debug_mode=False)
                
                loss = 0
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:
                        masked_outputs = outputs[i][mask]
                        masked_labels = y[i][mask]
                        loss += criterion(masked_outputs, masked_labels)
                
                loss = loss / len(protein_masks)
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
            
            # 收集预测和标签
            batch_preds = []
            batch_labels = []
            
            for i in range(len(protein_masks)):
                mask = protein_masks[i].bool()
                if mask.sum() > 0:
                    masked_outputs = outputs[i][mask]
                    masked_labels = y[i][mask]
                    
                    probs = torch.sigmoid(masked_outputs)
                    batch_preds.append(probs.detach().cpu().numpy())
                    batch_labels.append(masked_labels.detach().cpu().numpy())
            
            total_loss += loss.item()
            all_preds.extend(batch_preds)
            all_labels.extend(batch_labels)
        
        # 计算指标
        if all_preds and all_labels:
            preds_flat = np.concatenate([p.flatten() for p in all_preds])
            labels_flat = np.concatenate([l.flatten() for l in all_labels])
            
            if len(np.unique(labels_flat)) > 1:
                auc, auprc, mcc, acc, precision, recall, f1 = Metric(preds_flat, labels_flat)
                
                if auc > best_auc:
                    best_auc = auc
                if auprc > best_auprc:
                    best_auprc = auprc
                
                if epoch % 10 == 0 or epoch < 10:
                    print(f"Epoch {epoch+1:3d}: Loss={total_loss:.6f}, AUC={auc:.4f}, AUPRC={auprc:.4f}, F1={f1:.4f}")
                
                if auc > 0.95 and convergence_epoch == -1:
                    convergence_epoch = epoch + 1
                    print(f"🎯 收敛到AUC>0.95: Epoch {convergence_epoch}")
    
    print(f"📊 最终结果: 最佳AUC={best_auc:.4f}, 最佳AUPRC={best_auprc:.4f}, 收敛轮次={convergence_epoch if convergence_epoch > 0 else '未收敛'}")
    return best_auc, best_auprc, convergence_epoch

def main():
    parser = argparse.ArgumentParser(description='SAM Optimizer Ablation Study')
    parser.add_argument('--seed', type=int, default=2024, help='Random seed')
    args = parser.parse_args()
    
    Seed_everything(args.seed)
    
    print("加载蛋白质数据...")
    protein_data = load_protein_data_from_tensors(max_proteins=4)
    print(f"加载了 {len(protein_data)} 个蛋白质的特征数据")
    
    # 基础配置（使用最佳设置）
    base_config = {
        'node_features': 1024 + 14 + 20,
        'edge_features': 16,
        'hidden_dim': 128,
        'num_encoder_layers': 2,
        'k_neighbors': 30,
        'augment_eps': 0.0,
        'dropout': 0.1,
        'debug_mode': False,  # 启用PE融合
        'loss_type': 'bce',   # 使用最佳损失函数
    }
    
    # 测试不同的优化器
    optimizer_configs = [
        {'type': 'adam', 'lr': 1e-3, 'weight_decay': 1e-5, 'name': 'Adam优化器'},
        {'type': 'sam', 'lr': 1e-3, 'weight_decay': 1e-5, 'rho': 0.05, 'name': 'SAM优化器 (rho=0.05)'},
        {'type': 'sam', 'lr': 1e-3, 'weight_decay': 1e-5, 'rho': 0.1, 'name': 'SAM优化器 (rho=0.1)'},
    ]
    
    results = []
    
    for optimizer_config in optimizer_configs:
        test_name = optimizer_config.pop('name')
        
        best_auc, best_auprc, convergence_epoch = test_optimizer(protein_data, base_config, optimizer_config, test_name)
        results.append({
            'optimizer': test_name,
            'best_auc': best_auc,
            'best_auprc': best_auprc,
            'convergence_epoch': convergence_epoch
        })
    
    # 总结结果
    print(f"\n{'='*70}")
    print("🏆 SAM优化器消融实验结果总结")
    print(f"{'='*70}")
    
    for result in results:
        print(f"{result['optimizer']:20s}: AUC={result['best_auc']:.4f}, AUPRC={result['best_auprc']:.4f}, 收敛轮次={result['convergence_epoch'] if result['convergence_epoch'] > 0 else '未收敛'}")
    
    # 分析最佳优化器
    best_auc_result = max(results, key=lambda x: x['best_auc'])
    best_auprc_result = max(results, key=lambda x: x['best_auprc'])
    
    print(f"\n🥇 最佳AUC: {best_auc_result['optimizer']} (AUC={best_auc_result['best_auc']:.4f})")
    print(f"🥇 最佳AUPRC: {best_auprc_result['optimizer']} (AUPRC={best_auprc_result['best_auprc']:.4f})")
    
    # SAM vs Adam 比较
    adam_result = next((r for r in results if 'Adam' in r['optimizer']), None)
    sam_results = [r for r in results if 'SAM' in r['optimizer']]
    
    if adam_result and sam_results:
        print(f"\n📊 SAM vs Adam 比较:")
        for sam_result in sam_results:
            auc_diff = sam_result['best_auc'] - adam_result['best_auc']
            auprc_diff = sam_result['best_auprc'] - adam_result['best_auprc']
            print(f"   {sam_result['optimizer']}: AUC{auc_diff:+.4f}, AUPRC{auprc_diff:+.4f}")
        
        best_sam = max(sam_results, key=lambda x: x['best_auc'])
        if best_sam['best_auc'] > adam_result['best_auc']:
            print("✅ SAM优化器提升了模型性能")
        else:
            print("🔄 SAM优化器未显著提升性能，可能需要调整参数")

if __name__ == '__main__':
    main()
