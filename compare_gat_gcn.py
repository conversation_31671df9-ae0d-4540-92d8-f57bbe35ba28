import torch
import time
from model import MVGN<PERSON>

def benchmark_model(use_sparse_gat=True):
    # 临时修改PYGEOMETRIC_AVAILABLE
    if not use_sparse_gat:
        import self_attention
        original_value = self_attention.PYGEOMETRIC_AVAILABLE
        self_attention.PYGEOMETRIC_AVAILABLE = False
    
    model = MVGNN(1058, 16, 128, 2, 30, 0.15, 0.4).cuda()
    
    # 测试数据
    B, N = 4, 100  # 中等规模
    X = torch.randn(B, N, 3).cuda()
    V = torch.randn(B, N, 1058).cuda()
    mask = torch.ones(B, N).cuda()
    adj = torch.rand(B, N, N).cuda()
    adj = (adj > 0.8).float()
    pe = torch.randn(B, N, 32).cuda()
    
    # 预热
    with torch.no_grad():
        _ = model(X, V, mask, adj, pe)
    
    # 计时测试
    torch.cuda.synchronize()
    start_time = time.time()
    
    with torch.no_grad():
        for _ in range(5):
            output = model(X, V, mask, adj, pe)
    
    torch.cuda.synchronize()
    end_time = time.time()
    
    avg_time = (end_time - start_time) / 5
    memory_used = torch.cuda.max_memory_allocated() / 1024**3
    
    # 恢复设置
    if not use_sparse_gat:
        self_attention.PYGEOMETRIC_AVAILABLE = original_value
    
    return avg_time, memory_used

print("🔍 GAT vs GCN 性能对比")
print("=" * 40)

try:
    print("测试稀疏GAT...")
    gat_time, gat_memory = benchmark_model(use_sparse_gat=True)
    print(f"稀疏GAT - 时间: {gat_time:.3f}s, 内存: {gat_memory:.2f}GB")
except Exception as e:
    print(f"稀疏GAT测试失败: {e}")
    gat_time, gat_memory = float('inf'), float('inf')

try:
    print("测试GCN...")
    gcn_time, gcn_memory = benchmark_model(use_sparse_gat=False)
    print(f"GCN - 时间: {gcn_time:.3f}s, 内存: {gcn_memory:.2f}GB")
except Exception as e:
    print(f"GCN测试失败: {e}")
    gcn_time, gcn_memory = float('inf'), float('inf')

print("\n📊 对比结果:")
if gat_time != float('inf') and gcn_time != float('inf'):
    speedup = gcn_time / gat_time
    memory_ratio = gat_memory / gcn_memory
    print(f"速度比: {speedup:.2f}x ({'GAT更快' if speedup > 1 else 'GCN更快'})")
    print(f"内存比: {memory_ratio:.2f}x ({'GAT用更多' if memory_ratio > 1 else 'GCN用更多'})")
